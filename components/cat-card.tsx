"use client";

import Image from "next/image";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Edit,
	Trash2,
	Clock,
	Heart,
	MapPin,
	User,
	Loader2,
} from "lucide-react";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";
import { CatStatusMenu } from "@/components/cat-status-menu";
import { ClinicCardBadge } from "@/components/clinic-verification-badge";
import { CatSummary } from "@/lib/types/cat";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";

// Extend CatSummary with isFavorite for the UI
export type CatCardProps = CatSummary & {
	isFavorite?: boolean;
};

export type CatCardActions = {
	mode: "view" | "manage";
	onDelete?: (cat: { id: string; name: string }) => void;
	onToggleDraft?: (id: string, isDraft: boolean) => void;
	onToggleFavorite?: (id: string) => void;
	togglePending?: boolean;
	onStatusChange?: (id: string, status: string) => void;
};

export function CatCard({
	cat,
	actions,
}: {
	cat: CatCardProps;
	actions: CatCardActions;
}) {
	const t = useTranslations("cats");
	const buttonsT = useTranslations("buttons");

	return (
		<Card className="overflow-hidden h-full card-hover rounded-2xl border border-gray-200 bg-white shadow-sm group">
			<div className="relative overflow-hidden">
				<Link
					href={`/cats/${cat.slug}`}
					className="relative block h-60 w-full bg-gray-100"
				>
					<Image
						src={cat.imageUrl || "/cat.jpeg"}
						alt={cat.name}
						fill
						className="object-cover transition-transform duration-300 group-hover:scale-105"
						onError={(e) => {
							e.currentTarget.src = "/cat.jpeg";
						}}
					/>
				</Link>
				{actions.mode === "manage" && (
					<div className="absolute top-3 right-3">
						{cat.isDraft ? (
							<Badge className="bg-gray-500 text-white">
								{t("status.draft")}
							</Badge>
						) : (
							<CatStatusMenu
								catId={cat.id}
								currentStatus={
									cat.status ||
									(cat.adopted ? "adopted" : "available")
								}
								onStatusChange={(status) =>
									actions.onStatusChange?.(cat.id, status)
								}
							/>
						)}
					</div>
				)}
				{actions.mode === "view" && (
					<Button
						variant="ghost"
						size="icon"
						className={cn(
							"absolute top-4 right-4 bg-white/90 hover:bg-white p-2 rounded-full shadow-md transition-all touch-target",
							actions.togglePending && "pointer-events-none"
						)}
						onClick={(e) => {
							e.preventDefault();
							actions.onToggleFavorite?.(cat.id);
						}}
						disabled={actions.togglePending}
					>
						{actions.togglePending ? (
							<Loader2 className="h-5 w-5 animate-spin text-gray-500" />
						) : (
							<Heart
								className={cn(
									"w-5 h-5 transition-colors",
									cat.isFavorite
										? "fill-red-500 text-red-500"
										: "text-gray-600 hover:text-red-500"
								)}
								fill={cat.isFavorite ? "currentColor" : "none"}
								strokeWidth={cat.isFavorite ? 0 : 2}
							/>
						)}
					</Button>
				)}
			</div>
			<CardContent className="p-6 grow">
				<div className="flex justify-between items-start mb-2">
					<Link href={`/cats/${cat.slug}`}>
						<h3 className="text-xl font-display font-semibold text-gray-900 group-hover:text-teal-600 transition-colors">
							{cat.name}
						</h3>
					</Link>
					<Avatar className="h-8 w-8">
						<AvatarFallback
							className={cn(
								cat.gender === "female"
									? "bg-pink-100 text-pink-500"
									: "bg-blue-100 text-blue-500"
							)}
						>
							<User className="h-4 w-4" />
						</AvatarFallback>
					</Avatar>
				</div>

				<p className="text-gray-600 mb-4">
					{cat.age} • {cat.breed}
				</p>

				{cat.description && (
					<p className="text-gray-700 text-sm line-clamp-2 mb-4 group-hover:text-gray-800 transition-colors">
						{cat.description}
					</p>
				)}

				<div className="flex items-center text-sm text-gray-600 mb-3">
					<MapPin className="w-4 h-4 mr-1 flex-shrink-0" />
					<span className="truncate">
						{cat.location || t("location.unknown")}
					</span>
				</div>

				{/* Clinic Badge */}
				{cat.user?.role === "clinic" && cat.user.clinicProfile && (
					<div className="flex items-center gap-2 mb-3">
						<ClinicCardBadge
							status={
								cat.user.clinicProfile.status as
									| "pending"
									| "approved"
									| "rejected"
							}
							featured={cat.user.clinicProfile.featured}
						/>
						{cat.user.clinicProfile.name && (
							<span className="text-xs text-gray-500 truncate">
								{cat.user.clinicProfile.name}
							</span>
						)}
					</div>
				)}
				{cat.isDraft && (
					<div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-md flex items-center">
						<Clock className="h-4 w-4 text-amber-500 mr-2" />
						<span className="text-sm text-amber-700">
							{t("status.draftMessage")}
						</span>
					</div>
				)}
			</CardContent>
			{actions.mode === "manage" && (
				<CardFooter className="p-4 pt-0 border-t mt-auto">
					<div className="flex w-full justify-between items-center">
						<div className="flex gap-2">
							<Button variant="outline" size="sm" asChild>
								<Link href={`/cats/${cat.slug}/edit`}>
									<Edit className="h-3.5 w-3.5 mr-1" />
									{buttonsT("edit")}
								</Link>
							</Button>
							<Button
								variant="outline"
								size="sm"
								className="text-red-500 hover:text-red-600 hover:border-red-200"
								onClick={() =>
									actions.onDelete?.({
										id: cat.id,
										name: cat.name,
									})
								}
							>
								<Trash2 className="h-3.5 w-3.5 mr-1" />
								{buttonsT("delete")}
							</Button>
						</div>
						<div>
							{cat.isDraft ? (
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										actions.onToggleDraft?.(
											cat.id,
											Boolean(cat.isDraft)
										)
									}
									disabled={actions.togglePending}
								>
									{t("actions.publish")}
								</Button>
							) : (
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										actions.onToggleDraft?.(
											cat.id,
											Boolean(cat.isDraft)
										)
									}
									disabled={actions.togglePending}
								>
									{t("actions.unpublish")}
								</Button>
							)}
						</div>
					</div>
				</CardFooter>
			)}
		</Card>
	);
}
