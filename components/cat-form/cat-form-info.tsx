"use client";

import * as React from "react";
import { Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Card,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle,
	CardDescription,
} from "@/components/ui/card";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useCatForm } from "./cat-form-provider";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";

interface CatFormInfoProps {
	isMobile: boolean;
	methods: any;
}

export function CatFormInfo({ isMobile, methods }: CatFormInfoProps) {
	const {
		form,
		saveDraft,
		isSubmitting,
		isSavingDraft,
		handleWilayaChange,
		selectedWilayaId,
		breeds,
		isLoadingBreeds,
		wilayas,
		isLoadingWilayas,
		communes,
		isLoadingCommunes,
		watchSpecialNeeds,
		validateInfoStep,
	} = useCatForm();
	const formT = useTranslations("forms.catForm");
	const breedsT = useTranslations("breeds");

	return (
		<Card className="border-none shadow-none">
			<CardContent className="space-y-8 px-0">
				<Form {...form}>
					<form className="space-y-8">
						{/* Basic Information */}
						<div className="space-y-6">
							<div className="flex items-center">
								<h3 className="text-lg font-medium">
									{formT("info.basicInfo")}
								</h3>
								<Separator className="flex-1 ml-3" />
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{formT("info.name")}
											</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder={formT(
														"info.namePlaceholder"
													)}
													className={cn(
														"transition-all duration-200 focus-within:border-primary",
														form.formState.errors
															.name &&
															"border-destructive"
													)}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="age"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{formT("info.age")}
											</FormLabel>
											<FormControl>
												<Input
													{...field}
													onChange={(e) =>
														field.onChange(
															Number(
																e.target.value
															)
														)
													}
													type="number"
													placeholder={formT(
														"info.agePlaceholder"
													)}
													className={cn(
														"transition-all duration-200 focus-within:border-primary",
														form.formState.errors
															.age &&
															"border-destructive"
													)}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="gender"
								render={({ field }) => (
									<FormItem className="space-y-3">
										<FormLabel>
											{formT("info.gender")}
										</FormLabel>
										<FormControl>
											<RadioGroup
												onValueChange={field.onChange}
												defaultValue={field.value}
												className="flex flex-row space-x-4"
											>
												<FormItem className="flex items-center space-x-2 space-y-0">
													<FormControl>
														<RadioGroupItem value="male" />
													</FormControl>
													<FormLabel className="font-normal">
														{formT("info.male")}
													</FormLabel>
												</FormItem>
												<FormItem className="flex items-center space-x-2 space-y-0">
													<FormControl>
														<RadioGroupItem value="female" />
													</FormControl>
													<FormLabel className="font-normal">
														{formT("info.female")}
													</FormLabel>
												</FormItem>
											</RadioGroup>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="breedId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{formT("info.breed")}
										</FormLabel>
										<Select
											onValueChange={field.onChange}
											defaultValue={field.value}
										>
											<FormControl>
												<SelectTrigger
													className={cn(
														"transition-all duration-200 focus:border-primary",
														form.formState.errors
															.breedId &&
															"border-destructive"
													)}
												>
													<SelectValue
														placeholder={formT(
															"info.breedPlaceholder"
														)}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{isLoadingBreeds ? (
													<SelectItem
														value="loading"
														disabled
													>
														<Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
														{formT(
															"info.loadingBreeds"
														)}
													</SelectItem>
												) : (
													breeds?.map(
														(breed: any) => (
															<SelectItem
																key={breed.id}
																value={breed.id.toString()}
															>
																{breedsT(
																	breed.name
																) || breed.name}
															</SelectItem>
														)
													)
												)}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="description"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{formT("info.description")}
										</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												placeholder={formT(
													"info.descriptionPlaceholder"
												)}
												className={cn(
													"resize-none min-h-[120px] transition-all duration-200 focus-within:border-primary",
													form.formState.errors
														.description &&
														"border-destructive"
												)}
												rows={4}
											/>
										</FormControl>
										<FormDescription>
											{formT(
												"info.descriptionDescription"
											)}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<FormField
								control={form.control}
								name="story"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{formT("info.story")}
										</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												placeholder={formT(
													"info.storyPlaceholder"
												)}
												className="resize-none min-h-[120px] transition-all duration-200 focus-within:border-primary"
												rows={4}
											/>
										</FormControl>
										<FormDescription>
											{formT("info.storyDescription")}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Location */}
						<div className="space-y-6">
							<div className="flex items-center">
								<h3 className="text-lg font-medium">
									{formT("info.location")}
								</h3>
								<Separator className="flex-1 ml-3" />
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="wilayaId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{formT("info.wilaya")}
											</FormLabel>
											<Select
												onValueChange={
													handleWilayaChange
												}
												defaultValue={field.value}
											>
												<FormControl>
													<SelectTrigger
														className={cn(
															"transition-all duration-200 focus:border-primary",
															form.formState
																.errors
																.wilayaId &&
																"border-destructive"
														)}
													>
														<SelectValue
															placeholder={formT(
																"info.wilayaPlaceholder"
															)}
														/>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{isLoadingWilayas ? (
														<SelectItem
															value="loading"
															disabled
														>
															<Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
															{formT(
																"info.loadingWilayas"
															)}
														</SelectItem>
													) : (
														wilayas?.map(
															(wilaya: any) => (
																<SelectItem
																	key={
																		wilaya.id
																	}
																	value={wilaya.id.toString()}
																>
																	{
																		wilaya.code
																	}{" "}
																	-{" "}
																	{
																		wilaya.name
																	}
																</SelectItem>
															)
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="communeId"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{formT("info.commune")}
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												defaultValue={field.value}
												disabled={!selectedWilayaId}
											>
												<FormControl>
													<SelectTrigger
														className={cn(
															"transition-all duration-200 focus:border-primary",
															form.formState
																.errors
																.communeId &&
																"border-destructive"
														)}
													>
														<SelectValue
															placeholder={
																selectedWilayaId
																	? formT(
																			"info.communePlaceholder"
																		)
																	: formT(
																			"info.selectWilayaFirst"
																		)
															}
														/>
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{isLoadingCommunes ? (
														<SelectItem
															value="loading"
															disabled
														>
															<Loader2 className="h-4 w-4 animate-spin mr-2 inline" />
															{formT(
																"info.loadingCommunes"
															)}
														</SelectItem>
													) : (
														communes?.map(
															(commune: any) => (
																<SelectItem
																	key={
																		commune.id
																	}
																	value={commune.id.toString()}
																>
																	{
																		commune.name
																	}
																</SelectItem>
															)
														)
													)}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						{/* Health & Status */}
						<div className="space-y-6">
							<div className="flex items-center">
								<h3 className="text-lg font-medium">
									{formT("info.health")}
								</h3>
								<Separator className="flex-1 ml-3" />
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="vaccinated"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={
														field.onChange
													}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>
													{formT("info.vaccinated")}
												</FormLabel>
												<FormDescription>
													{formT(
														"info.vaccinatedDescription"
													)}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="neutered"
									render={({ field }) => (
										<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
											<FormControl>
												<Checkbox
													checked={field.value}
													onCheckedChange={
														field.onChange
													}
												/>
											</FormControl>
											<div className="space-y-1 leading-none">
												<FormLabel>
													{formT("info.neutered")}
												</FormLabel>
												<FormDescription>
													{formT(
														"info.neuteredDescription"
													)}
												</FormDescription>
											</div>
										</FormItem>
									)}
								/>
							</div>

							<FormField
								control={form.control}
								name="specialNeeds"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel>
												{formT("info.specialNeeds")}
											</FormLabel>
											<FormDescription>
												{formT(
													"info.specialNeedsDescription"
												)}
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>

							{watchSpecialNeeds && (
								<FormField
									control={form.control}
									name="specialNeedsDescription"
									render={({ field }) => (
										<FormItem>
											<FormLabel>
												{formT(
													"info.specialNeedsDescription"
												)}
											</FormLabel>
											<FormControl>
												<Textarea
													{...field}
													placeholder={formT(
														"info.specialNeedsPlaceholder"
													)}
													className="resize-none transition-all duration-200 focus-within:border-primary"
													rows={3}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							)}

							<FormField
								control={form.control}
								name="adopted"
								render={({ field }) => (
									<FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
										<FormControl>
											<Checkbox
												checked={field.value}
												onCheckedChange={field.onChange}
											/>
										</FormControl>
										<div className="space-y-1 leading-none">
											<FormLabel>
												{formT("info.adopted")}
											</FormLabel>
											<FormDescription>
												{formT(
													"info.adoptedDescription"
												)}
											</FormDescription>
										</div>
									</FormItem>
								)}
							/>
						</div>
					</form>
				</Form>
			</CardContent>
			<CardFooter className="flex justify-between px-0 pt-4">
				<div />
				<div className="flex gap-2">
					<Button
						type="button"
						variant="outline"
						onClick={saveDraft}
						disabled={isSubmitting}
					>
						{isSubmitting && isSavingDraft ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								{formT("buttons.saving")}
							</>
						) : (
							formT("buttons.saveDraft")
						)}
					</Button>
					<Button
						type="button"
						onClick={async () => {
							// First trigger all validations
							const isValid = await validateInfoStep();
							if (isValid) {
								methods.next();
							} else {
								// Scroll to the first error
								const firstError =
									document.querySelector(".text-destructive");
								if (firstError) {
									firstError.scrollIntoView({
										behavior: "smooth",
										block: "center",
									});
								}
							}
						}}
					>
						{formT("info.nextStep")}
					</Button>
				</div>
			</CardFooter>
		</Card>
	);
}
