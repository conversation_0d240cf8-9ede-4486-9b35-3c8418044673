"use client";

import { Badge } from "@/components/ui/badge";
import { Stethoscope, Star, CheckCircle, Clock, XCircle } from "lucide-react";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";

interface ClinicVerificationBadgeProps {
	status: "pending" | "approved" | "rejected";
	featured?: boolean;
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
	className?: string;
}

export function ClinicVerificationBadge({
	status,
	featured = false,
	size = "md",
	showIcon = true,
	className,
}: ClinicVerificationBadgeProps) {
	const t = useTranslations("cats.clinic");

	// Determine badge content based on status and featured flag
	const getBadgeConfig = () => {
		if (status === "approved" && featured) {
			return {
				text: t("featured"),
				variant: "secondary" as const,
				className: "bg-yellow-100 text-yellow-800 border-yellow-200",
				icon: Star,
			};
		}

		if (status === "approved") {
			return {
				text: t("verified"),
				variant: "secondary" as const,
				className: "bg-teal-100 text-teal-800 border-teal-200",
				icon: CheckCircle,
			};
		}

		if (status === "pending") {
			return {
				text: t("pending"),
				variant: "outline" as const,
				className: "bg-amber-50 text-amber-700 border-amber-200",
				icon: Clock,
			};
		}

		if (status === "rejected") {
			return {
				text: t("rejected"),
				variant: "outline" as const,
				className: "bg-red-50 text-red-700 border-red-200",
				icon: XCircle,
			};
		}

		// Default fallback
		return {
			text: t("verified"),
			variant: "outline" as const,
			className: "bg-gray-50 text-gray-700 border-gray-200",
			icon: Stethoscope,
		};
	};

	const config = getBadgeConfig();
	const Icon = config.icon;

	// Size-based styling
	const sizeClasses = {
		sm: "text-xs px-2 py-1",
		md: "text-sm px-2.5 py-1",
		lg: "text-base px-3 py-1.5",
	};

	const iconSizes = {
		sm: "h-3 w-3",
		md: "h-4 w-4",
		lg: "h-5 w-5",
	};

	return (
		<Badge
			variant={config.variant}
			className={cn(
				config.className,
				sizeClasses[size],
				"inline-flex items-center gap-1 font-medium",
				className
			)}
		>
			{showIcon && <Icon className={iconSizes[size]} />}
			{config.text}
		</Badge>
	);
}

// Convenience components for specific use cases
export function ClinicCardBadge({
	status,
	featured,
	className,
}: {
	status: "pending" | "approved" | "rejected";
	featured?: boolean;
	className?: string;
}) {
	return (
		<ClinicVerificationBadge
			status={status}
			featured={featured}
			size="sm"
			className={className}
		/>
	);
}

export function ClinicProfileBadge({
	status,
	featured,
	className,
}: {
	status: "pending" | "approved" | "rejected";
	featured?: boolean;
	className?: string;
}) {
	return (
		<ClinicVerificationBadge
			status={status}
			featured={featured}
			size="md"
			className={className}
		/>
	);
}
