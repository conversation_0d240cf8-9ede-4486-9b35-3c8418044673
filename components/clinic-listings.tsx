"use client";

import { Loader2 } from "lucide-react";
import { api } from "@/lib/trpc/react";
import { ClinicCard } from "@/components/clinic-card";
import { Pagination } from "@/components/pagination";
import { useTranslations } from "next-intl";
import { clinicListingParsers } from "@/lib/search-params";
import { useQueryStates } from "nuqs";
import { memo, useMemo } from "react";

// Helper function to filter out falsy values
function filterFalsyValues<T extends Record<string, any>>(obj: T): Partial<T> {
	return Object.fromEntries(
		Object.entries(obj).filter(([, value]) => {
			if (Array.isArray(value)) {
				return value.length > 0;
			}
			return (
				value !== undefined &&
				value !== null &&
				value !== "" &&
				value !== false
			);
		})
	) as Partial<T>;
}

export const ClinicListings = memo(function ClinicListings() {
	// Get parsed search parameters from nuqs client-side hook
	const [filters] = useQueryStates(clinicListingParsers);

	// Extract individual filter values
	const { search, page, wilayaId, communeId, featured, services, sort } =
		filters;

	const t = useTranslations("clinics");

	// Prepare query parameters with initial processing
	const queryParams = {
		query: search && search.trim() ? search.trim() : undefined,
		page,
		wilayaId:
			wilayaId && wilayaId !== "all" ? parseInt(wilayaId) : undefined,
		communeId:
			communeId && communeId !== "all" ? parseInt(communeId) : undefined,
		featured: featured || undefined,
		services: services.length > 0 ? services : undefined,
		limit: 12,
		offset: (page - 1) * 12,
	};

	// Filter out falsy values before passing to the query
	const filteredParams = filterFalsyValues(queryParams);

	// Fetch clinics using tRPC
	const { data, isLoading, error } = api.clinics.getAll.useQuery(
		filteredParams,
		{
			staleTime: 30000, // 30 seconds
			refetchOnWindowFocus: false, // Prevent unnecessary refetches
			keepPreviousData: true, // Keep previous data while loading new data
			retry: 2, // Retry failed requests twice
		}
	);

	if (isLoading) {
		return (
			<div className="flex justify-center items-center py-12">
				<Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center py-12">
				<h3 className="text-xl font-medium mb-2 text-destructive">
					{t("error")}
				</h3>
				<p className="text-muted-foreground">
					{error.message || t("errorLoadingClinics")}
				</p>
			</div>
		);
	}

	if (!data || data.clinics.length === 0) {
		return (
			<div className="w-full">
				<div className="text-center py-12">
					<h3 className="text-xl font-medium mb-2">
						{t("noClinicsFound")}
					</h3>
					<p className="text-muted-foreground mb-6">
						{search
							? t("search.tryDifferentTerms")
							: t("adjustFilters")}
					</p>
				</div>
			</div>
		);
	}

	// Calculate total pages for pagination
	const totalPages = Math.ceil(data.total / 12);

	// Memoize clinic cards to prevent unnecessary re-renders
	const clinicCards = useMemo(
		() =>
			data.clinics.map((clinic) => (
				<ClinicCard key={clinic.id} clinic={clinic} />
			)),
		[data.clinics]
	);

	return (
		<div className="w-full">
			{/* Results count */}
			<div className="mb-6">
				<p className="text-sm text-muted-foreground">
					{t("showingResults", {
						count: data.clinics.length,
						total: data.total,
					})}
				</p>
			</div>

			{/* Clinic Grid */}
			<div
				className="clinic-grid"
				role="region"
				aria-label="Clinic listings"
				aria-live="polite"
				aria-busy={isLoading}
			>
				{clinicCards}
			</div>

			{/* Pagination */}
			{totalPages > 1 && (
				<Pagination currentPage={page} totalPages={totalPages} />
			)}
		</div>
	);
});
