"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
	FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { api } from "@/lib/trpc/react";
import { Loader2, MapPin, Phone, Globe, Stethoscope } from "lucide-react";
import type { User } from "@/lib/types/profile";
import type { ClinicProfile } from "@/lib/types/clinic";
import { clinicProfileUpdateSchema } from "@/lib/types/clinic";
import type { z } from "zod";
import { ServicesSelector } from "./services-selector";
import { OperatingHoursEditor } from "./operating-hours-editor";

type ClinicProfileFormValues = z.infer<typeof clinicProfileUpdateSchema>;

interface ClinicProfileFormProps {
	user: User;
	existingProfile?: ClinicProfile | null;
}

export function ClinicProfileForm({ existingProfile }: ClinicProfileFormProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const { toast } = useToast();
	const t = useTranslations("profile.clinic");
	const commonT = useTranslations("common");

	// tRPC mutation for updating clinic profile
	const utils = api.useUtils();
	const updateProfile = api.clinics.updateProfile.useMutation({
		onSuccess: () => {
			// Invalidate queries to refetch updated data
			utils.clinics.getProfile.invalidate();
			utils.users.getProfile.invalidate();

			toast({
				title: t("profileUpdated") || "Profile updated",
				description:
					t("profileUpdatedDescription") ||
					"Your clinic profile has been updated successfully.",
			});

			setIsSubmitting(false);
		},
		onError: (error) => {
			toast({
				title: commonT("error") || "Error",
				description:
					error.message ||
					t("profileUpdateError") ||
					"Your clinic profile couldn't be updated. Please try again.",
				variant: "destructive",
			});

			setIsSubmitting(false);
		},
	});

	// State for wilaya/commune selection
	const [selectedWilayaId, setSelectedWilayaId] = useState<
		string | undefined
	>(
		existingProfile?.wilayaId
			? existingProfile.wilayaId.toString()
			: undefined
	);

	// Fetch wilayas and communes using tRPC
	const { data: wilayas, isLoading: isLoadingWilayas } =
		api.location.getWilayas.useQuery();
	const { data: communes, isLoading: isLoadingCommunes } =
		api.location.getCommunesByWilaya.useQuery(
			{
				wilayaId: selectedWilayaId
					? parseInt(selectedWilayaId)
					: undefined,
			},
			{ enabled: !!selectedWilayaId }
		);

	// Form setup with react-hook-form and zod validation
	const form = useForm<ClinicProfileFormValues>({
		resolver: zodResolver(clinicProfileUpdateSchema),
		defaultValues: {
			name: existingProfile?.name || "",
			address: existingProfile?.address || "",
			wilayaId: existingProfile?.wilayaId || undefined,
			communeId: existingProfile?.communeId || undefined,
			phone: existingProfile?.phone || "",
			website: existingProfile?.website || "",
			services: existingProfile?.services || [],
			operatingHours: existingProfile?.operatingHours || undefined,
		},
	});

	// Form submission handler
	async function onSubmit(values: ClinicProfileFormValues) {
		setIsSubmitting(true);
		updateProfile.mutate(values);
	}

	return (
		<div className="space-y-6">
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-6"
					aria-label="Clinic profile form"
				>
					{/* Basic Information */}
					<fieldset className="bg-white border border-gray-200 rounded-2xl p-6">
						<legend className="text-lg font-display font-semibold text-gray-900 mb-4 flex items-center">
							<MapPin className="w-5 h-5 mr-2 text-teal-600" />
							{t("basicInfo") || "Basic Information"}
						</legend>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Clinic Name */}
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("clinicName") || "Clinic Name"}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder={
													t(
														"clinicNamePlaceholder"
													) || "Your clinic name"
												}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent min-h-[44px] touch-manipulation"
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Wilaya */}
							<FormField
								control={form.control}
								name="wilayaId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("state") || "Wilaya"}
										</FormLabel>
										<Select
											onValueChange={(value) => {
												const wilayaId = value
													? parseInt(value)
													: undefined;
												field.onChange(wilayaId);
												setSelectedWilayaId(value);
												// Reset commune when wilaya changes
												form.setValue(
													"communeId",
													undefined
												);
											}}
											value={
												field.value?.toString() || ""
											}
											disabled={
												isSubmitting || isLoadingWilayas
											}
										>
											<FormControl>
												<SelectTrigger className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent">
													<SelectValue
														placeholder={
															t(
																"statePlaceholder"
															) || "Select wilaya"
														}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{wilayas?.map((wilaya) => (
													<SelectItem
														key={wilaya.id}
														value={wilaya.id.toString()}
													>
														{wilaya.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Commune */}
							<FormField
								control={form.control}
								name="communeId"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("city") || "Commune"}
										</FormLabel>
										<Select
											onValueChange={(value) => {
												const communeId = value
													? parseInt(value)
													: undefined;
												field.onChange(communeId);
											}}
											value={
												field.value?.toString() || ""
											}
											disabled={
												isSubmitting ||
												isLoadingCommunes ||
												!selectedWilayaId
											}
										>
											<FormControl>
												<SelectTrigger className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent">
													<SelectValue
														placeholder={
															!selectedWilayaId
																? t(
																		"selectWilayaFirst"
																	) ||
																	"Select wilaya first"
																: t(
																		"cityPlaceholder"
																	) ||
																	"Select commune"
														}
													/>
												</SelectTrigger>
											</FormControl>
											<SelectContent>
												{communes?.map((commune) => (
													<SelectItem
														key={commune.id}
														value={commune.id.toString()}
													>
														{commune.name}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Address */}
						<div className="mt-4">
							<FormField
								control={form.control}
								name="address"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("address") || "Address"}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder={
													t("addressPlaceholder") ||
													"Full address"
												}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent min-h-[44px] touch-manipulation"
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</fieldset>

					{/* Contact Information */}
					<div className="bg-white border border-gray-200 rounded-2xl p-6">
						<h3 className="text-lg font-display font-semibold text-gray-900 mb-4 flex items-center">
							<Phone className="w-5 h-5 mr-2 text-teal-600" />
							{t("contactInfo") || "Contact Information"}
						</h3>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{/* Phone */}
							<FormField
								control={form.control}
								name="phone"
								render={({ field }) => (
									<FormItem>
										<FormLabel>
											{t("phone") || "Phone"}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder={
													t("phonePlaceholder") ||
													"Phone number"
												}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent min-h-[44px] touch-manipulation"
												disabled={isSubmitting}
												type="tel"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>

						{/* Website */}
						<div className="mt-4">
							<FormField
								control={form.control}
								name="website"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="flex items-center">
											<Globe className="w-4 h-4 mr-2" />
											{t("website") || "Website"}
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												type="url"
												placeholder={
													t("websitePlaceholder") ||
													"https://your-clinic-website.com"
												}
												className="px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-transparent min-h-[44px] touch-manipulation"
												disabled={isSubmitting}
											/>
										</FormControl>
										<FormDescription>
											{t("websiteHelp") ||
												"Optional: Your clinic's website URL"}
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>

					{/* Services */}
					<div className="bg-white border border-gray-200 rounded-2xl p-6">
						<h3 className="text-lg font-display font-semibold text-gray-900 mb-4 flex items-center">
							<Stethoscope className="w-5 h-5 mr-2 text-teal-600" />
							{t("services") || "Services"}
						</h3>

						<FormField
							control={form.control}
							name="services"
							render={({ field }) => (
								<FormItem>
									<FormLabel>
										{t("servicesOffered") ||
											"Services Offered"}
									</FormLabel>
									<FormControl>
										<ServicesSelector
											selectedServices={field.value || []}
											onServicesChange={field.onChange}
											disabled={isSubmitting}
										/>
									</FormControl>
									<FormDescription>
										{t("servicesHelp") ||
											"Select the services your clinic offers to help clients find you."}
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Operating Hours */}
					<div className="bg-white border border-gray-200 rounded-2xl p-6">
						<FormField
							control={form.control}
							name="operatingHours"
							render={({ field }) => (
								<FormItem>
									<FormControl>
										<OperatingHoursEditor
											operatingHours={field.value}
											onOperatingHoursChange={
												field.onChange
											}
											disabled={isSubmitting}
										/>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{/* Submit Button */}
					<div className="flex justify-end">
						<Button
							type="submit"
							disabled={isSubmitting}
							className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-xl font-medium min-h-[44px]"
						>
							{isSubmitting && (
								<Loader2 className="w-4 h-4 mr-2 animate-spin" />
							)}
							{existingProfile
								? t("updateProfile") || "Update Profile"
								: t("createProfile") || "Create Profile"}
						</Button>
					</div>
				</form>
			</Form>
		</div>
	);
}
