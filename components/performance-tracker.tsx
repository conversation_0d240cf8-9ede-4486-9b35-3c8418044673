"use client";

import { useEffect } from "react";
import { trackPageLoad, trackMemoryUsage } from "@/lib/utils/performance";

interface PerformanceTrackerProps {
	pageName: string;
	trackMemory?: boolean;
}

export function PerformanceTracker({ 
	pageName, 
	trackMemory = false 
}: PerformanceTrackerProps) {
	useEffect(() => {
		// Track page load performance
		trackPageLoad(pageName);
		
		// Track memory usage if requested
		if (trackMemory) {
			trackMemoryUsage(pageName);
		}
	}, [pageName, trackMemory]);

	// This component doesn't render anything
	return null;
}
