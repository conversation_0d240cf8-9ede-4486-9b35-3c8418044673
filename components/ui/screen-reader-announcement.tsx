"use client";

import { useEffect, useRef } from "react";

interface ScreenReaderAnnouncementProps {
	message: string;
	priority?: "polite" | "assertive";
}

export function ScreenReaderAnnouncement({
	message,
	priority = "polite",
}: ScreenReaderAnnouncementProps) {
	const announcementRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (message && announcementRef.current) {
			// Clear previous message
			announcementRef.current.textContent = "";
			
			// Set new message after a brief delay to ensure screen readers pick it up
			setTimeout(() => {
				if (announcementRef.current) {
					announcementRef.current.textContent = message;
				}
			}, 100);
		}
	}, [message]);

	return (
		<div
			ref={announcementRef}
			aria-live={priority}
			aria-atomic="true"
			className="sr-only"
		/>
	);
}

// Hook for managing screen reader announcements
export function useScreenReaderAnnouncement() {
	const announcementRef = useRef<HTMLDivElement>(null);

	const announce = (message: string, priority: "polite" | "assertive" = "polite") => {
		if (announcementRef.current) {
			announcementRef.current.setAttribute("aria-live", priority);
			announcementRef.current.textContent = "";
			
			setTimeout(() => {
				if (announcementRef.current) {
					announcementRef.current.textContent = message;
				}
			}, 100);
		}
	};

	const AnnouncementComponent = () => (
		<div
			ref={announcementRef}
			aria-live="polite"
			aria-atomic="true"
			className="sr-only"
		/>
	);

	return { announce, AnnouncementComponent };
}
