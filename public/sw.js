// Service Worker for caching clinic data and images
const CACHE_NAME = 'clinic-cache-v1';
const CLINIC_API_CACHE = 'clinic-api-cache-v1';
const IMAGE_CACHE = 'clinic-images-v1';

// URLs to cache on install
const STATIC_CACHE_URLS = [
	'/clinics',
	'/api/trpc/clinics.getAll',
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
	event.waitUntil(
		caches.open(CACHE_NAME).then((cache) => {
			return cache.addAll(STATIC_CACHE_URLS);
		})
	);
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
	event.waitUntil(
		caches.keys().then((cacheNames) => {
			return Promise.all(
				cacheNames.map((cacheName) => {
					if (cacheName !== CACHE_NAME && 
						cacheName !== CLINIC_API_CACHE && 
						cacheName !== IMAGE_CACHE) {
						return caches.delete(cacheName);
					}
				})
			);
		})
	);
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', (event) => {
	const { request } = event;
	const url = new URL(request.url);

	// Handle clinic API requests
	if (url.pathname.includes('/api/trpc/clinics')) {
		event.respondWith(
			caches.open(CLINIC_API_CACHE).then((cache) => {
				return cache.match(request).then((cachedResponse) => {
					if (cachedResponse) {
						// Serve from cache and update in background
						fetch(request).then((response) => {
							if (response.ok) {
								cache.put(request, response.clone());
							}
						});
						return cachedResponse;
					}
					
					// Not in cache, fetch and cache
					return fetch(request).then((response) => {
						if (response.ok) {
							cache.put(request, response.clone());
						}
						return response;
					});
				});
			})
		);
		return;
	}

	// Handle image requests
	if (request.destination === 'image') {
		event.respondWith(
			caches.open(IMAGE_CACHE).then((cache) => {
				return cache.match(request).then((cachedResponse) => {
					if (cachedResponse) {
						return cachedResponse;
					}
					
					return fetch(request).then((response) => {
						if (response.ok) {
							cache.put(request, response.clone());
						}
						return response;
					});
				});
			})
		);
		return;
	}

	// Handle other requests with network-first strategy
	event.respondWith(
		fetch(request).catch(() => {
			return caches.match(request);
		})
	);
});
