import { notFound, redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { NewMessageClient } from "./new-message-client";

interface NewMessagePageProps {
	params: Promise<{
		lang: Locale;
	}>;
	searchParams: Promise<{
		clinic?: string;
		cat?: string;
	}>;
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const resolvedParams = await params;
	const t = await getTranslations({
		locale: resolvedParams.lang,
		namespace: "profile.messages",
	});
	const common = await getTranslations({ locale: resolvedParams.lang, namespace: "common" });

	return {
		title: `${t("startConversation")} - ${common("appName")}`,
		description: t("startConversation"),
	};
}

export default async function NewMessagePage({ 
	params, 
	searchParams 
}: NewMessagePageProps) {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;
	const { lang } = resolvedParams;
	const { clinic: clinicSlug, cat: catId } = resolvedSearchParams;

	// Enable static rendering
	setRequestLocale(lang);

	// Must have either clinic or cat parameter
	if (!clinicSlug && !catId) {
		notFound();
	}

	// Pass the parameters to the client component
	return (
		<NewMessageClient 
			clinicSlug={clinicSlug} 
			catId={catId}
		/>
	);
}
