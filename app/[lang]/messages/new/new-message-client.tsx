"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardDescription,
	CardFooter,
} from "@/components/ui/card";

import {
	Send,
	ArrowLeft,
	Shield,
	Stethoscope,
	MessageSquare,
} from "lucide-react";
import { api } from "@/lib/trpc/react";
import { useSession } from "@/lib/auth/client";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/i18n/navigation";
import { cn } from "@/lib/utils";
import { useToast } from "@/components/ui/use-toast";

interface NewMessageClientProps {
	clinicSlug?: string;
	catId?: string;
}

export function NewMessageClient({ clinicSlug, catId }: NewMessageClientProps) {
	const router = useRouter();
	const { data: session } = useSession();
	const t = useTranslations("chat");
	const messagesT = useTranslations("profile.messages");
	const { toast } = useToast();

	const [message, setMessage] = useState("");
	const [selectedTemplate, setSelectedTemplate] = useState("general");

	// Define message templates with translations
	const messageTemplates = {
		general: t("templates.general"),
		services: t("templates.services"),
		appointment: t("templates.appointment"),
	};

	// Fetch clinic data if clinicSlug is provided
	const {
		data: clinicData,
		isLoading: isLoadingClinic,
		error: clinicError,
	} = api.messages.findClinicUser.useQuery(
		{ clinicSlug: clinicSlug || "" },
		{
			enabled: !!clinicSlug,
			retry: false,
			onError: (error) => {
				toast({
					title: t("errorTitle"),
					description: error.message,
					variant: "destructive",
				});
			},
		}
	);

	// Fetch cat owner data if catId is provided
	const {
		data: catData,
		isLoading: isLoadingCat,
		error: catError,
	} = api.messages.checkChatStatus.useQuery(
		{ catId: catId || "" },
		{
			enabled: !!catId,
			retry: false,
			onError: (error) => {
				toast({
					title: t("errorTitle"),
					description: error.message,
					variant: "destructive",
				});
			},
		}
	);

	// Create chat mutation
	const { mutate: createChat, isPending: isCreatingChat } =
		api.messages.createChat.useMutation({
			onSuccess: (data) => {
				toast({
					title: t("successTitle"),
					description: t("successDescription"),
				});

				// Redirect to the new chat
				router.push(`/messages/${data.chatId}`);
			},
			onError: (error) => {
				toast({
					title: t("errorTitle"),
					description: error.message,
					variant: "destructive",
				});
			},
		});

	// Update message when template changes
	useEffect(() => {
		if (
			selectedTemplate &&
			messageTemplates[selectedTemplate as keyof typeof messageTemplates]
		) {
			setMessage(
				messageTemplates[
					selectedTemplate as keyof typeof messageTemplates
				]
			);
		}
	}, [selectedTemplate, messageTemplates]);

	// Handle sending message
	const handleSendMessage = () => {
		if (!message.trim()) return;

		if (clinicData) {
			createChat({
				recipientId: clinicData.userId,
				initialMessage: message,
			});
		} else if (catData && !catData.isOwner && catData.ownerName) {
			// If chat already exists, redirect to it
			if (catData.chatExists && catData.chatId) {
				router.push(`/messages/${catData.chatId}`);
				return;
			}

			// For cat messaging, we need to get the owner ID from the cat data
			// This will be handled by the existing createChat logic
			createChat({
				recipientId: "0", // Will be determined by catId
				catId: catId,
				initialMessage: message,
			});
		}
	};

	// Loading state
	if (isLoadingClinic || isLoadingCat) {
		return (
			<div className="flex flex-col h-full items-center justify-center p-8">
				<div className="animate-pulse flex flex-col items-center">
					<div className="h-16 w-16 bg-gray-200 rounded-full mb-4"></div>
					<div className="h-6 w-48 bg-gray-200 rounded mb-2"></div>
					<div className="h-4 w-32 bg-gray-200 rounded"></div>
				</div>
			</div>
		);
	}

	// Error state
	if (clinicError || catError || (!clinicData && !catData)) {
		return (
			<div className="flex flex-col h-full items-center justify-center p-8">
				<Card className="w-full max-w-md">
					<CardHeader>
						<CardTitle>{t("errorTitle")}</CardTitle>
						<CardDescription>
							{clinicError?.message ||
								catError?.message ||
								t("errorDescription")}
						</CardDescription>
					</CardHeader>
					<CardFooter>
						<Button asChild>
							<Link href="/messages">
								<ArrowLeft className="mr-2 h-4 w-4" />
								{messagesT("viewAllMessages")}
							</Link>
						</Button>
					</CardFooter>
				</Card>
			</div>
		);
	}

	// If user is the cat owner, show error
	if (catData?.isOwner) {
		return (
			<div className="flex flex-col h-full items-center justify-center p-8">
				<Card className="w-full max-w-md">
					<CardHeader>
						<CardTitle>{t("errorTitle")}</CardTitle>
						<CardDescription>
							{t("cannotMessageYourself")}
						</CardDescription>
					</CardHeader>
					<CardFooter>
						<Button asChild>
							<Link href="/messages">
								<ArrowLeft className="mr-2 h-4 w-4" />
								{messagesT("viewAllMessages")}
							</Link>
						</Button>
					</CardFooter>
				</Card>
			</div>
		);
	}

	// If chat already exists for this cat, redirect to it
	if (catData?.chatExists && catData?.chatId) {
		return (
			<div className="flex flex-col h-full items-center justify-center p-8">
				<Card className="w-full max-w-md">
					<CardHeader>
						<CardTitle>{t("continueConversation")}</CardTitle>
						<CardDescription>
							{t("continueConversationWith", {
								name: catData.ownerName,
							})}
						</CardDescription>
					</CardHeader>
					<CardFooter>
						<Button asChild>
							<Link href={`/messages/${catData.chatId}`}>
								<MessageSquare className="mr-2 h-4 w-4" />
								{t("continueConversation")}
							</Link>
						</Button>
					</CardFooter>
				</Card>
			</div>
		);
	}

	return (
		<div className="flex flex-col h-full">
			{/* Header */}
			<div className="border-b border-gray-200 p-4 md:p-6">
				<div className="flex items-center">
					<Button
						variant="ghost"
						size="icon"
						className="mr-2 md:mr-4"
						asChild
					>
						<Link href="/messages">
							<ArrowLeft className="h-5 w-5" />
						</Link>
					</Button>

					<div className="flex items-center">
						<Avatar className="h-10 w-10 mr-3">
							<AvatarImage
								src={clinicData?.userImage || ""}
								alt={
									clinicData?.clinicName ||
									catData?.ownerName ||
									""
								}
							/>
							<AvatarFallback>
								{clinicData ? (
									<Stethoscope className="h-5 w-5" />
								) : (
									<MessageSquare className="h-5 w-5" />
								)}
							</AvatarFallback>
						</Avatar>

						<div>
							<div className="flex items-center">
								<h2 className="text-lg font-semibold">
									{clinicData?.clinicName ||
										catData?.ownerName ||
										""}
								</h2>

								{(clinicData?.isVerified || false) && (
									<Shield className="h-4 w-4 text-teal-500 ml-1" />
								)}
							</div>

							<p className="text-sm text-gray-500">
								{clinicData ? messagesT("verified") : ""}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Message area */}
			<div className="flex-1 overflow-y-auto p-4 md:p-6">
				<Card>
					<CardHeader>
						<CardTitle>{t("startConversation")}</CardTitle>
						<CardDescription>
							{t("dialogDescription")}
						</CardDescription>
					</CardHeader>

					{clinicData && (
						<CardContent>
							<div className="mb-4">
								<p className="text-sm text-gray-600 mb-2">
									Quick templates:
								</p>
								<div className="flex flex-wrap gap-2">
									<Button
										variant={
											selectedTemplate === "general"
												? "default"
												: "outline"
										}
										size="sm"
										onClick={() =>
											setSelectedTemplate("general")
										}
									>
										{t("templateLabels.general")}
									</Button>
									<Button
										variant={
											selectedTemplate === "services"
												? "default"
												: "outline"
										}
										size="sm"
										onClick={() =>
											setSelectedTemplate("services")
										}
									>
										{t("templateLabels.services")}
									</Button>
									<Button
										variant={
											selectedTemplate === "appointment"
												? "default"
												: "outline"
										}
										size="sm"
										onClick={() =>
											setSelectedTemplate("appointment")
										}
									>
										{t("templateLabels.appointment")}
									</Button>
								</div>
							</div>
						</CardContent>
					)}

					<CardContent>
						<Textarea
							value={message}
							onChange={(e) => setMessage(e.target.value)}
							placeholder={t("messagePlaceholder")}
							className="min-h-[120px]"
						/>
					</CardContent>

					<CardFooter className="flex justify-between">
						<Button variant="outline" asChild>
							<Link href="/messages">Cancel</Link>
						</Button>

						<Button
							onClick={handleSendMessage}
							disabled={!message.trim() || isCreatingChat}
						>
							{isCreatingChat ? (
								<>{t("sending")}</>
							) : (
								<>
									<Send className="mr-2 h-4 w-4" />
									{t("sendMessage")}
								</>
							)}
						</Button>
					</CardFooter>
				</Card>
			</div>
		</div>
	);
}
