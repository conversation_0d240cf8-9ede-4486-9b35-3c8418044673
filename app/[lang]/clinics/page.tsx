import { ClinicListings } from "@/components/clinic-listings";
import { ClinicSearchFiltersWrapper } from "@/components/clinic-search-filters-wrapper";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import {
	Breadcrumb,
	BreadcrumbList,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Link } from "@/lib/i18n/navigation";
import { Home } from "lucide-react";

export async function generateMetadata({
	params,
}: {
	params: { lang: Locale };
}) {
	const { lang } = await params;
	const t = await getTranslations({ locale: lang, namespace: "clinics" });
	const common = await getTranslations({ locale: lang, namespace: "common" });

	return {
		title: `${t("title")} - ${common("appName")}`,
		description: t("description"),
		openGraph: {
			title: `${t("title")} - ${common("appName")}`,
			description: t("description"),
			type: "website",
		},
		twitter: {
			card: "summary_large_image",
			title: `${t("title")} - ${common("appName")}`,
			description: t("description"),
		},
	};
}

export default async function ClinicsPage({
	params,
}: {
	params: Promise<{ lang: Locale }>;
}) {
	const { lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	// Get translations
	const t = await getTranslations("clinics");
	const common = await getTranslations("common");

	return (
		<main className="w-full flex justify-center py-10 bg-gray-50">
			{/* Skip Navigation Link */}
			<a
				href="#clinic-listings"
				className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-primary text-primary-foreground px-4 py-2 rounded-md z-50"
			>
				Skip to clinic listings
			</a>

			<div className="container max-w-7xl px-4 sm:px-6 lg:px-8">
				{/* Breadcrumb Navigation */}
				<Breadcrumb className="mb-6">
					<BreadcrumbList>
						<BreadcrumbItem>
							<BreadcrumbLink asChild>
								<Link
									href="/"
									className="flex items-center gap-1"
								>
									<Home className="h-4 w-4" />
									{common("navigation.home")}
								</Link>
							</BreadcrumbLink>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<BreadcrumbPage>{t("title")}</BreadcrumbPage>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>

				<div className="flex justify-between items-center mb-6">
					<h1 className="text-2xl font-bold">{t("title")}</h1>
				</div>

				<p className="text-muted-foreground mb-6 text-center sm:text-left">
					{t("description")}
				</p>

				{/* Search and Filters - Responsive Layout */}
				<ClinicSearchFiltersWrapper />

				<div id="clinic-listings">
					<ClinicListings />
				</div>
			</div>
		</main>
	);
}
