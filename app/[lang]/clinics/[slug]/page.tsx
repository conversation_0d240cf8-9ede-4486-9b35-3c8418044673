import { notFound } from "next/navigation";
import { api } from "@/lib/trpc/server";
import { getTranslations } from "next-intl/server";
import { setRequestLocale } from "next-intl/server";
import type { Locale } from "@/lib/i18n/routing";
import { PublicClinicProfile } from "@/components/public-clinic-profile";
import {
	<PERSON>readcrumb,
	BreadcrumbList,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Link } from "@/lib/i18n/navigation";
import { Home, Stethoscope } from "lucide-react";

interface ClinicProfilePageProps {
	params: Promise<{
		slug: string;
		lang: Locale;
	}>;
}

async function getClinic(slug: string) {
	try {
		return await api.clinics.getBySlug(slug);
	} catch (error) {
		return null;
	}
}

export async function generateMetadata({
	params,
}: {
	params: Promise<{ slug: string; lang: Locale }>;
}) {
	const { slug, lang } = await params;
	const clinic = await getClinic(slug);
	const common = await getTranslations({ locale: lang, namespace: "common" });
	const t = await getTranslations({ locale: lang, namespace: "clinics" });

	if (!clinic) {
		return {
			title: `${t("notFound")} - ${common("appName")}`,
			description: t("notFoundDescription"),
		};
	}
	const location = clinic.address;
	const description = `${clinic.name} - ${t("profileDescription", { location })}`;

	return {
		title: `${clinic.name} - ${t("veterinaryClinic")} | ${common("appName")}`,
		description,
		openGraph: {
			title: `${clinic.name} - ${t("veterinaryClinic")}`,
			description,
			type: "website",
			url: `/clinics/${slug}`,
		},
		twitter: {
			card: "summary_large_image",
			title: `${clinic.name} - ${t("veterinaryClinic")}`,
			description,
		},
		alternates: {
			canonical: `/clinics/${slug}`,
		},
	};
}

export default async function ClinicProfilePage({
	params,
}: ClinicProfilePageProps) {
	const { slug, lang } = await params;

	// Enable static rendering
	setRequestLocale(lang);

	const clinic = await getClinic(slug);

	if (!clinic) {
		notFound();
	}

	// Only show approved clinics to the public
	if (clinic.status !== "approved") {
		notFound();
	}

	// Get translations for breadcrumb
	const common = await getTranslations({ locale: lang, namespace: "common" });
	const clinicsT = await getTranslations({
		locale: lang,
		namespace: "clinics",
	});

	return (
		<>
			<main className="container mx-auto px-4 py-8 max-w-6xl">
				{/* Breadcrumb Navigation */}
				<Breadcrumb className="mb-6">
					<BreadcrumbList>
						<BreadcrumbItem>
							<BreadcrumbLink asChild>
								<Link
									href="/"
									className="flex items-center gap-1"
								>
									<Home className="h-4 w-4" />
									{common("navigation.home")}
								</Link>
							</BreadcrumbLink>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<BreadcrumbLink asChild>
								<Link
									href="/clinics"
									className="flex items-center gap-1"
								>
									<Stethoscope className="h-4 w-4" />
									{clinicsT("title")}
								</Link>
							</BreadcrumbLink>
						</BreadcrumbItem>
						<BreadcrumbSeparator />
						<BreadcrumbItem>
							<BreadcrumbPage>{clinic.name}</BreadcrumbPage>
						</BreadcrumbItem>
					</BreadcrumbList>
				</Breadcrumb>

				<PublicClinicProfile clinic={clinic} />
			</main>
		</>
	);
}
