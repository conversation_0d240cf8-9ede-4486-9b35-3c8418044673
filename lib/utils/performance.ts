/**
 * Performance monitoring utilities for clinic features
 */

// Performance metrics tracking
export interface PerformanceMetrics {
	loadTime: number;
	renderTime: number;
	interactionTime?: number;
	memoryUsage?: number;
}

// Track page load performance
export function trackPageLoad(pageName: string) {
	if (typeof window === "undefined") return;

	const startTime = performance.now();
	
	// Track when page is fully loaded
	window.addEventListener("load", () => {
		const loadTime = performance.now() - startTime;
		
		// Log performance metrics (in production, send to analytics)
		console.log(`[Performance] ${pageName} load time: ${loadTime.toFixed(2)}ms`);
		
		// Track Core Web Vitals
		trackCoreWebVitals(pageName);
	});
}

// Track Core Web Vitals
export function trackCoreWebVitals(pageName: string) {
	if (typeof window === "undefined") return;

	// Track Largest Contentful Paint (LCP)
	if ("PerformanceObserver" in window) {
		const observer = new PerformanceObserver((list) => {
			const entries = list.getEntries();
			const lastEntry = entries[entries.length - 1];
			
			console.log(`[Performance] ${pageName} LCP: ${lastEntry.startTime.toFixed(2)}ms`);
		});
		
		observer.observe({ entryTypes: ["largest-contentful-paint"] });
	}
}

// Track component render time
export function trackComponentRender(componentName: string) {
	const startTime = performance.now();
	
	return () => {
		const renderTime = performance.now() - startTime;
		console.log(`[Performance] ${componentName} render time: ${renderTime.toFixed(2)}ms`);
	};
}

// Debounce function for performance optimization
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout;
	
	return (...args: Parameters<T>) => {
		clearTimeout(timeout);
		timeout = setTimeout(() => func(...args), wait);
	};
}

// Throttle function for performance optimization
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number
): (...args: Parameters<T>) => void {
	let inThrottle: boolean;
	
	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			setTimeout(() => (inThrottle = false), limit);
		}
	};
}

// Memory usage tracking
export function trackMemoryUsage(componentName: string) {
	if (typeof window === "undefined" || !("memory" in performance)) return;

	const memory = (performance as any).memory;
	console.log(`[Performance] ${componentName} memory usage:`, {
		used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
		total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)} MB`,
		limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)} MB`,
	});
}

// Image loading optimization
export function preloadImage(src: string): Promise<void> {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.onload = () => resolve();
		img.onerror = reject;
		img.src = src;
	});
}

// Batch image preloading
export async function preloadImages(srcs: string[]): Promise<void> {
	const promises = srcs.map(preloadImage);
	await Promise.allSettled(promises);
}

// Check if user prefers reduced motion
export function prefersReducedMotion(): boolean {
	if (typeof window === "undefined") return false;
	return window.matchMedia("(prefers-reduced-motion: reduce)").matches;
}

// Optimize animations based on user preference
export function getOptimizedAnimationDuration(defaultDuration: number): number {
	return prefersReducedMotion() ? 0 : defaultDuration;
}
