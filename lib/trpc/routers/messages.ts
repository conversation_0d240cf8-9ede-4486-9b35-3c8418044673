import { z } from "zod";
import { createTR<PERSON><PERSON>outer as router, protectedProcedure } from "../trpc";
import {
	chats,
	chatParticipants,
	messages,
	users,
	catImages,
	clinicProfiles,
} from "@/lib/db/schema";
import { eq, and, desc, count, inArray, lt, not, isNull } from "drizzle-orm";
import { TRPCError } from "@trpc/server";
import { logSlowQuery } from "./helpers/cat-helpers";
import { chatHelpers } from "./helpers/chat-helpers";

// Schema for creating a chat
const createChatSchema = z.object({
	recipientId: z.string(),
	catId: z.string().optional(),
	initialMessage: z.string().min(1, "Message cannot be empty"),
});

// Schema for sending a message
const sendMessageSchema = z.object({
	chatId: z.string(),
	content: z.string().min(1, "Message cannot be empty"),
});

// Schema for checking chat status for a specific cat
const checkChatStatusSchema = z.object({
	catId: z.string(),
});

// Schema for finding clinic user by slug
const findClinicUserSchema = z.object({
	clinicSlug: z.string(),
});

export const messagesRouter = router({
	// Optimized query for ChatButton - only checks if conversation exists
	checkChatStatus: protectedProcedure
		.input(checkChatStatusSchema)
		.query(async ({ ctx, input }) => {
			const catId = parseInt(input.catId);
			const userId = parseInt(ctx.user.id);
			const startTime = performance.now();

			// Get cat owner info
			const catOwner = await chatHelpers.getCatOwnerInfo(ctx.db, catId);

			if (!catOwner) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Cat not found",
				});
			}

			// Check if user is the cat owner
			if (catOwner.id === userId) {
				return {
					isOwner: true,
					chatExists: false,
					chatId: null,
					ownerName: null,
				};
			}

			// Check if conversation already exists
			const existingChat = await chatHelpers.findExistingChatForCat(
				ctx.db,
				catId,
				userId
			);
			const duration = performance.now() - startTime;
			logSlowQuery("checkChatStatus", duration);

			return {
				isOwner: false,
				chatExists: !!existingChat,
				chatId: existingChat?.id.toString() || null,
				ownerName: catOwner.name,
			};
		}),

	// Find clinic user by slug for messaging
	findClinicUser: protectedProcedure
		.input(findClinicUserSchema)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();

			// URL decode the slug to handle Arabic and other special characters
			const decodedSlug = decodeURIComponent(input.clinicSlug);

			const clinic = await ctx.db.query.clinicProfiles.findFirst({
				where: eq(clinicProfiles.slug, decodedSlug),
				with: {
					user: {
						columns: {
							id: true,
							name: true,
							slug: true,
							role: true,
							image: true,
						},
					},
				},
				columns: {
					id: true,
					name: true,
					slug: true,
					status: true,
				},
			});

			if (!clinic) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Only allow messaging approved clinics
			if (clinic.status !== "approved") {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Clinic not found",
				});
			}

			// Don't allow users to message themselves
			if (clinic.user.id === parseInt(ctx.user.id)) {
				throw new TRPCError({
					code: "BAD_REQUEST",
					message: "Cannot message yourself",
				});
			}

			const duration = performance.now() - startTime;
			logSlowQuery("findClinicUser", duration);

			return {
				userId: clinic.user.id.toString(),
				userName: clinic.user.name,
				userSlug: clinic.user.slug,
				userImage: clinic.user.image,
				clinicName: clinic.name,
				clinicSlug: clinic.slug,
				isVerified: clinic.user.role === "clinic",
			};
		}),

	// Create a new chat
	createChat: protectedProcedure
		.input(createChatSchema)
		.mutation(async ({ ctx, input }) => {
			let recipientId = parseInt(input.recipientId);

			// If catId is provided and recipientId is 0, find the cat owner
			if (input.catId && recipientId === 0) {
				const catOwner = await chatHelpers.getCatOwnerInfo(
					ctx.db,
					parseInt(input.catId)
				);
				if (!catOwner) {
					throw new TRPCError({
						code: "NOT_FOUND",
						message: "Cat not found",
					});
				}
				recipientId = catOwner.id;
			}

			// Check if recipient exists
			const recipient = await ctx.db.query.users.findFirst({
				where: eq(users.id, recipientId),
			});

			if (!recipient) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Recipient not found",
				});
			}

			let existingChat = null;

			// If catId is provided, check for existing conversation about this specific cat
			if (input.catId) {
				existingChat = await chatHelpers.findExistingChatForCat(
					ctx.db,
					parseInt(input.catId),
					parseInt(ctx.user.id)
				);
			} else {
				// If no catId, check for any existing chat between these users (legacy behavior)
				const existingChats = await ctx.db.query.chats.findMany({
					where: isNull(chats.catId), // Only check chats without a specific cat
					with: {
						participants: true,
					},
				});

				// Find a chat where both users are participants
				existingChat = existingChats.find((chat) => {
					const participantIds = chat.participants.map(
						(p) => p.userId
					);
					return (
						participantIds.includes(parseInt(ctx.user.id)) &&
						participantIds.includes(recipientId)
					);
				});
			}

			let chatId;

			if (existingChat) {
				// Use existing chat
				chatId = existingChat.id;
			} else {
				// Create a new chat
				const [newChat] = await ctx.db
					.insert(chats)
					.values({
						catId: input.catId ? parseInt(input.catId) : null,
					})
					.returning();

				if (!newChat) {
					throw new TRPCError({
						code: "INTERNAL_SERVER_ERROR",
						message: "Failed to create chat",
					});
				}

				// Add both users to the chat
				await ctx.db.insert(chatParticipants).values([
					{
						chatId: newChat.id,
						userId: parseInt(ctx.user.id),
					},
					{
						chatId: newChat.id,
						userId: recipientId,
					},
				]);

				chatId = newChat.id;
			}

			// Add the initial message
			await ctx.db.insert(messages).values({
				chatId,
				userId: parseInt(ctx.user.id),
				content: input.initialMessage,
			});

			return {
				chatId: chatId.toString(),
				message: "Chat created successfully",
			};
		}),

	// Get messages for a specific chat (optimized version)
	getChatMessages: protectedProcedure
		.input(
			z.object({
				chatId: z.string(),
				limit: z.number().min(1).max(100).default(50),
				cursor: z.string().optional(), // For pagination
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const chatId = parseInt(input.chatId);
			const userId = parseInt(ctx.user.id);

			// Step 1: Validate chat access using optimized helper (single query)
			const accessValidation = await chatHelpers.validateChatAccess(
				ctx.db,
				chatId,
				userId
			);

			if (!accessValidation.valid) {
				throw new TRPCError({
					code:
						accessValidation.error === "Chat not found"
							? "NOT_FOUND"
							: "FORBIDDEN",
					message: accessValidation.error,
				});
			}

			// Step 2: Get optimized chat details with selective field fetching
			const chat = await ctx.db.query.chats.findFirst({
				where: eq(chats.id, chatId),
				columns: {
					id: true,
					catId: true,
					createdAt: true,
				},
				with: {
					// Only fetch essential participant info
					participants: {
						columns: {
							userId: true,
						},
						with: {
							user: {
								columns: {
									id: true,
									name: true,
								},
							},
						},
					},
				},
			});

			if (!chat) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Chat not found",
				});
			}

			// Step 3: Get messages with optimized query and selective user data
			const baseWhere = eq(messages.chatId, chatId);
			const messagesWhere = input.cursor
				? and(baseWhere, lt(messages.createdAt, new Date(input.cursor)))
				: baseWhere;

			const chatMessages = await ctx.db.query.messages.findMany({
				where: messagesWhere,
				orderBy: desc(messages.createdAt),
				limit: input.limit,
				columns: {
					id: true,
					content: true,
					createdAt: true,
					status: true,
					userId: true,
				},
				with: {
					user: {
						columns: {
							id: true,
							name: true,
						},
					},
				},
			});

			// Step 4: Batch update unread messages (if any)
			const unreadMessageIds = chatMessages
				.filter((msg) => msg.userId !== userId && msg.status !== "read")
				.map((msg) => msg.id);

			// Use Promise.all for concurrent operations
			const updatePromise =
				unreadMessageIds.length > 0
					? ctx.db
							.update(messages)
							.set({ status: "read" })
							.where(inArray(messages.id, unreadMessageIds))
					: Promise.resolve();

			// Execute update concurrently with response formatting
			await updatePromise;

			// Step 5: Format response efficiently
			const otherParticipant = chat.participants.find(
				(p) => p.userId !== userId
			);

			const duration = performance.now() - startTime;
			logSlowQuery("getChatMessages", duration, 500);

			return {
				chat: {
					id: chat.id.toString(),
					with: otherParticipant
						? {
								id: otherParticipant.user.id.toString(),
								name: otherParticipant.user.name || "User",
							}
						: null,
				},
				messages: chatMessages.map((msg) => ({
					id: msg.id.toString(),
					content: msg.content,
					timestamp: msg.createdAt.toISOString(),
					status: msg.status,
					isFromMe: msg.userId === userId,
					sender: {
						id: msg.user.id.toString(),
						name: msg.user.name || "User",
					},
				})),
				nextCursor:
					chatMessages.length === input.limit
						? chatMessages[
								chatMessages.length - 1
							].createdAt.toISOString()
						: undefined,
			};
		}),

	// Send a message in a chat
	sendMessage: protectedProcedure
		.input(sendMessageSchema)
		.mutation(async ({ ctx, input }) => {
			const chatId = parseInt(input.chatId);
			const userId = parseInt(ctx.user.id);

			// Check if user is a participant in this chat using helper
			const isParticipant = await chatHelpers.isUserParticipant(
				ctx.db,
				chatId,
				userId
			);

			if (!isParticipant) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: "You are not a participant in this chat",
				});
			}

			// Send the message
			const [newMessage] = await ctx.db
				.insert(messages)
				.values({
					chatId,
					userId: parseInt(ctx.user.id),
					content: input.content,
				})
				.returning();

			if (!newMessage) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
					message: "Failed to send message",
				});
			}

			// Note: tRPC/React Query will handle cache invalidation automatically

			return {
				id: newMessage.id.toString(),
				content: newMessage.content,
				timestamp: newMessage.createdAt.toISOString(),
				status: newMessage.status,
			};
		}),

	// Get user's conversations for listing (optimized - no message content)
	getMyConversations: protectedProcedure
		.input(
			z.object({
				page: z.number().default(1),
				limit: z.number().default(10),
			})
		)
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const userId = parseInt(ctx.user.id);

			// Step 1: Get user's chat IDs efficiently
			const userChatIds = await ctx.db
				.select({ chatId: chatParticipants.chatId })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, userId))
				.orderBy(desc(chatParticipants.createdAt))
				.limit(input.limit)
				.offset((input.page - 1) * input.limit);

			if (userChatIds.length === 0) {
				return {
					conversations: [],
					pagination: {
						total: 0,
						pageCount: 0,
						page: input.page,
						limit: input.limit,
					},
				};
			}

			const chatIds = userChatIds.map((c) => c.chatId);

			// Step 2: Get conversation data with optimized joins
			const conversationsData = await ctx.db.query.chats.findMany({
				where: inArray(chats.id, chatIds),
				with: {
					// Only fetch primary cat image
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
								columns: {
									url: true,
								},
							},
						},
					},
					// Get other participants (excluding current user)
					participants: {
						where: (participants) =>
							and(not(eq(participants.userId, userId))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
					// Get only the latest message for preview
					messages: {
						orderBy: desc(messages.createdAt),
						limit: 1,
						columns: {
							id: true,
							content: true,
							createdAt: true,
							status: true,
							userId: true,
						},
					},
				},
			});

			// Step 3: Get unread message counts in batch
			const unreadCounts = await chatHelpers.getBatchUnreadMessageCounts(
				ctx.db,
				chatIds,
				userId
			);

			// Step 4: Get total count for pagination
			const [{ value: totalCount }] = await ctx.db
				.select({ value: count() })
				.from(chatParticipants)
				.where(eq(chatParticipants.userId, userId));

			// Step 5: Format response efficiently
			const conversations = conversationsData.map((chat) => {
				const otherParticipant = chat.participants[0];
				const lastMessage = chat.messages[0];
				const unreadCount = unreadCounts.get(chat.id) || 0;

				return {
					id: chat.id.toString(),
					with: otherParticipant?.user
						? {
								id: otherParticipant.user.id.toString(),
								slug: otherParticipant.user.slug,
								name: otherParticipant.user.name,
								image: otherParticipant.user.image,
								isVerified:
									otherParticipant.user.role === "clinic" ||
									otherParticipant.user.role === "rescuer",
							}
						: null,
					cat: chat.cat
						? {
								id: chat.cat.id.toString(),
								slug: chat.cat.slug,
								name: chat.cat.name,
								imageUrl:
									chat.cat.images[0]?.url ||
									"/cat.jpeg?height=300&width=400",
							}
						: null,
					lastMessage: lastMessage
						? {
								text: lastMessage.content,
								timestamp: lastMessage.createdAt.toISOString(),
								isFromMe: lastMessage.userId === userId,
								isRead: lastMessage.status === "read",
							}
						: null,
					unreadCount,
				};
			});

			const duration = performance.now() - startTime;
			logSlowQuery("getMyConversations", duration, 300);

			return {
				conversations,
				pagination: {
					total: totalCount,
					pageCount: Math.ceil(totalCount / input.limit),
					page: input.page,
					limit: input.limit,
				},
			};
		}),

	// Get conversation header information (optimized for header display)
	getConversationHeader: protectedProcedure
		.input(z.object({ chatId: z.string() }))
		.query(async ({ ctx, input }) => {
			const startTime = performance.now();
			const userId = parseInt(ctx.user.id);
			const chatId = parseInt(input.chatId);

			// Validate chat access
			const validation = await chatHelpers.validateChatAccess(
				ctx.db,
				chatId,
				userId
			);
			if (!validation.valid) {
				throw new TRPCError({
					code: "FORBIDDEN",
					message: validation.error,
				});
			}

			// Get conversation header data with minimal joins
			const conversationData = await ctx.db.query.chats.findFirst({
				where: eq(chats.id, chatId),
				with: {
					// Get cat info for header
					cat: {
						columns: {
							id: true,
							slug: true,
							name: true,
							userId: true,
						},
						with: {
							images: {
								where: eq(catImages.isPrimary, true),
								limit: 1,
								columns: {
									url: true,
								},
							},
						},
					},
					// Get other participant info
					participants: {
						where: (participants) =>
							and(not(eq(participants.userId, userId))),
						with: {
							user: {
								columns: {
									id: true,
									slug: true,
									name: true,
									image: true,
									role: true,
								},
							},
						},
					},
				},
			});

			if (!conversationData) {
				throw new TRPCError({
					code: "NOT_FOUND",
					message: "Conversation not found",
				});
			}

			const otherParticipant = conversationData.participants[0];
			const duration = performance.now() - startTime;
			logSlowQuery("getConversationHeader", duration, 100);

			return {
				with: otherParticipant?.user
					? {
							id: otherParticipant.user.id.toString(),
							slug: otherParticipant.user.slug,
							name: otherParticipant.user.name,
							image: otherParticipant.user.image,
							isVerified:
								otherParticipant.user.role === "clinic" ||
								otherParticipant.user.role === "rescuer",
						}
					: null,
				cat: conversationData.cat
					? {
							id: conversationData.cat.id.toString(),
							slug: conversationData.cat.slug,
							name: conversationData.cat.name,
							imageUrl:
								conversationData.cat.images[0]?.url ||
								"/cat.jpeg?height=300&width=400",
							userId: conversationData.cat.userId?.toString(),
						}
					: null,
			};
		}),
});
