import {
	Sun,
	Moon,
	Monitor,
	User,
	Settings,
	LogOut,
	Home,
	Cat,
	Info,
	Mail,
	MessageCircleMore,
	Shield,
	Database,
	Stethoscope,
} from "lucide-react";
import { type Locale } from "@/lib/i18n/routing";
import { userRoleEnum } from "@/lib/db/schema";
import { useTranslations } from "next-intl";

/**
 * User role type for profile routing
 */
export type UserRole = (typeof userRoleEnum.enumValues)[number];

/**
 * Helper function to get the appropriate profile route based on user role
 * Matches the logic from app/[lang]/profile/page.tsx
 */
export const getProfileRouteForRole = (role: UserRole): string => {
	switch (role) {
		case "rescuer":
		case "clinic":
			// Rescuers and clinics start with their listed cats
			return "/profile/cats";
		case "adopter":
		case "admin":
		default:
			// Adopters and admins start with their favorites
			return "/profile/favorites";
	}
};

/**
 * Navigation route configuration types
 */
export interface NavRoute {
	label: string;
	href: string;
	icon?: React.ComponentType<{ className?: string }>;
	translationKey: string;
}

export interface UserMenuRoute {
	label: string;
	href: string;
	icon: React.ComponentType<{ className?: string }>;
	translationKey: string;
	translationNamespace?: string;
	action?: () => Promise<void>;
}

export interface AuthRoute {
	label: string;
	href: string;
	translationKey: string;
	variant?: "ghost" | "default";
}

/**
 * Main navigation routes configuration
 */
export const mainNavRoutes: Omit<NavRoute, "label">[] = [
	{
		href: "/",
		icon: Home,
		translationKey: "navigation.home",
	},
	{
		href: "/cats",
		icon: Cat,
		translationKey: "navigation.cats",
	},
	{
		href: "/clinics",
		icon: Stethoscope,
		translationKey: "navigation.clinics",
	},
	{
		href: "/about",
		icon: Info,
		translationKey: "navigation.about",
	},
	{
		href: "/contact",
		icon: Mail,
		translationKey: "navigation.contact",
	},
];

/**
 * User avatar popup menu routes configuration
 * Note: Profile route will be dynamically generated based on user role
 */
export const getUserMenuRoutesConfig = (
	userRole?: UserRole
): Omit<UserMenuRoute, "label">[] => {
	const baseRoutes: Omit<UserMenuRoute, "label">[] = [
		{
			href: userRole ? getProfileRouteForRole(userRole) : "/profile",
			icon: User,
			translationKey: "navigation.profile",
			translationNamespace: "common",
		},
		{
			href: "/messages",
			icon: MessageCircleMore,
			translationKey: "messages.title",
			translationNamespace: "profile",
		},
		{
			href: "/profile/settings",
			icon: Settings,
			translationKey: "settings.title",
			translationNamespace: "profile",
		},
	];

	// Add admin-specific routes for admin users
	if (userRole === "admin") {
		baseRoutes.splice(
			1,
			0,
			{
				href: "/admin",
				icon: Shield,
				translationKey: "admin.dashboard",
				translationNamespace: "common",
			},
			{
				href: "/admin/service-types",
				icon: Database,
				translationKey: "serviceTypes.title",
				translationNamespace: "admin",
			}
		);
	}

	// Add clinic-specific routes for clinic users
	if (userRole === "clinic") {
		baseRoutes.splice(
			1,
			0,
			{
				href: "/profile/clinic",
				icon: Stethoscope,
				translationKey: "clinicSettings",
				translationNamespace: "profile",
			},
			{
				href: "/profile/services",
				icon: Database,
				translationKey: "services",
				translationNamespace: "profile",
			}
		);
	}

	return baseRoutes;
};

/**
 * Authentication routes configuration
 */
export const authRoutes: Omit<AuthRoute, "label">[] = [
	{
		href: "/auth/login",
		translationKey: "navigation.login",
		variant: "ghost",
	},
	{
		href: "/auth/register",
		translationKey: "navigation.register",
		variant: "default",
	},
];

/**
 * Language configuration for navbar components
 */
export const languageConfig = {
	// Language display names
	names: {
		en: "English",
		fr: "Français",
		ar: "العربية",
	} as Record<Locale, string>,

	// Language flags (using emoji flags)
	flags: {
		en: "🇬🇧",
		fr: "🇫🇷",
		ar: "🇩🇿",
	} as Record<Locale, string>,
} as const;

/**
 * Theme configuration for navbar components
 */
export const themeConfig = {
	options: [
		{ key: "light", icon: Sun },
		{ key: "dark", icon: Moon },
		{ key: "system", icon: Monitor },
	] as const,
} as const;

/**
 * Helper functions to get routes with translated labels
 */
export const getMainNavRoutes = (t: (key: string) => string): NavRoute[] => {
	return mainNavRoutes.map((route) => ({
		...route,
		label: t(route.translationKey),
	}));
};

export const getUserMenuRoutes = (userRole?: UserRole): UserMenuRoute[] => {
	return getUserMenuRoutesConfig(userRole).map((route) => {
		const t = useTranslations(route.translationNamespace);
		return {
			...route,
			label: t(route.translationKey),
		};
	});
};

export const getAuthRoutes = (t: (key: string) => string): AuthRoute[] => {
	return authRoutes.map((route) => ({
		...route,
		label: t(route.translationKey),
	}));
};

/**
 * Helper function to get theme options with translated labels
 */
export const getThemeOptions = (tTheme: (key: string) => string) => {
	return themeConfig.options.map((option) => ({
		...option,
		label: tTheme(option.key),
	}));
};

/**
 * Logout action configuration
 */
export const logoutRoute: Omit<UserMenuRoute, "label"> = {
	href: "#",
	icon: LogOut,
	translationKey: "navigation.logout",
	translationNamespace: "common",
};
